+++
# --- Basic Metadata ---
id = "WF-MODE-CREATION-V1-12-QA"
title = "Quality Assurance (ACQA)"
step_number = 12
workflow_id = "WF-MODE-CREATION-V1"
next_step_id = "WF-MODE-CREATION-V1-13-USER-REVIEW" # Points to the next step file
prev_step_id = "WF-MODE-CREATION-V1-11-CREATE-KB-RULE" # Points to the previous step file
status = "active"
created_date = "2025-04-29"
updated_date = "2025-04-29"
version = "1.0"
tags = ["workflow-step", "quality-assurance", "qa", "acqa", "validation", "mode-creation"]

# --- Workflow Step Specific Fields ---
description = "Performs Quality Assurance checks on the generated mode artifacts using the Adaptive Confidence-based Quality Assurance (ACQA) process."
delegation_mode = "Coordinator (Roo Commander), potentially delegates to QA Agent (e.g., code-reviewer)" # Primary mode responsible for this step
# inputs = ["mode_slug", "mode_file_path", "kb_readme_path", "kb_rule_path", "agent_confidence_score", "user_caution_level"] # List of expected input data/context (optional)
# outputs = ["qa_status"] # List of expected output data/context (optional)
error_handling = "If issues are found, initiate corrections (loop back to relevant creation steps) and re-run QA. Persistent failures may trigger AFR process." # Specific error handling for this step (optional)
related_docs = [
    ".ruru/processes/acqa-process.md",
    ".ruru/processes/afr-process.md"
]
related_templates = [
    ".ruru/templates/toml-md/25_workflow_step_standard.md"
]
template_schema_doc = ".ruru/templates/toml-md/25_workflow_step_standard.README.md"
+++

# Step 12: Quality Assurance (ACQA)

**Responsible Mode:** Coordinator (Roo Commander), potentially delegates to QA Agent (e.g., `code-reviewer`)

## Procedure

1.  **Initiate ACQA:**
    *   Coordinator receives the generated artifacts (or paths to them) from the previous steps (Mode file, KB files/README, Rule file).
    *   Coordinator may also receive a *confidence score* from the agent(s) performing the creation steps.
    *   Coordinator initiates the Adaptive Confidence-based Quality Assurance (ACQA) process, as defined in `.ruru/processes/acqa-process.md`. This process uses the agent's confidence score (if available) and the User Caution Level to determine the depth of review required.

2.  **Perform Checks (Coordinator or Delegated QA Agent):**
    *   The ACQA process involves checks for:
        *   **Directory Structure & Naming:** Correct paths and names for `.ruru/modes/<new-slug>/`, `kb/`, and `.kilocode/rules-<slug>/`.
        *   **File Presence & Validity:** Presence and basic validity (e.g., non-empty, correct format) of required files:
            *   `.ruru/modes/<new-slug>/<new-slug>.mode.md`
            *   `.ruru/modes/<new-slug>/kb/README.md`
            *   `.kilocode/rules-<slug>/01-kb-lookup-rule.md`
            *   KB content files (if applicable, based on Step 05 decision).
        *   **Mode File Adherence:** The `.mode.md` file adheres to the standard template structure (`.ruru/templates/modes/00_standard_mode.md`) and includes populated Core Knowledge content (from Step 08).
        *   **Consistency:** Consistency between the mode definition (`.mode.md`), KB README, and KB lookup rule (e.g., paths, purpose).
        *   **Metadata Correctness:** Correct population of key metadata in `.mode.md` (ID, slug, classification).
        *   **Process Validity:** Review logs/reports from previous steps to check for errors during JSON parsing or file creation.

3.  **Handle Issues (Conditional):**
    *   **If Issues Found:**
        1.  Log the specific QA failures.
        2.  Initiate corrections by looping back to the relevant creation step(s) (e.g., Step 07, 08, 09, 10, 11) and delegating the fix.
        3.  After corrections are made, **re-run this QA step (Step 12)**.
        4.  If failures persist after retries, consider triggering the Adaptive Failure Resolution (AFR) process (`.ruru/processes/afr-process.md`) to diagnose root causes.
    *   **If No Issues Found:** Proceed to the next step.

4.  **Proceed:** Upon successful completion of QA checks, proceed to the next step: **[13_user_review.md](./13_user_review.md)**.

## Error Handling
*   As described in Step 3 above. Minor issues trigger corrections and re-QA. Repeated or significant failures should trigger the AFR process.