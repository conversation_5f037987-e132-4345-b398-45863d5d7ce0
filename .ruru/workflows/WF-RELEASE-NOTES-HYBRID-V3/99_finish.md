+++
# --- Step Metadata ---
step_id = "WF-RELEASE-NOTES-HYBRID-V3-99-FINISH" # Updated ID for V3
step_number = 99 # Convention for finish step
title = "Step 99: Finish Workflow & Report" # Title remains appropriate
description = """
Aggregates results based on the action performed ('add' or 'finalize').
Reports the path to the created incremental note file (for 'add') or the final release note file
and GitHub release URL (for 'finalize'). Generates the final workflow result summary.
"""

# --- Flow Control ---
depends_on = [
    # This step can be reached from either the 'add' or 'finalize' path.
    # The orchestrator needs to provide the relevant inputs based on which path was taken.
    "WF-RELEASE-NOTES-HYBRID-V3-01-GEN-INCREMENTAL", # Possible preceding step for 'add'
    "WF-RELEASE-NOTES-HYBRID-V3-98-FINALIZE" # Possible preceding step for 'finalize'
]
next_step = "" # Final step
error_step = "" # Final step, errors handled by reporting failure

# --- Execution ---
delegate_to = "" # Orchestrator handles final reporting

# --- Interface ---
inputs = [ # Data/artifacts needed. Only one set will be present depending on the path taken.
    "Output from step WF-RELEASE-NOTES-HYBRID-V3-00-START: determined_action", # To know which report format to use
    # Inputs if action was 'add':
    "Output from step WF-RELEASE-NOTES-HYBRID-V3-01-GEN-INCREMENTAL: incremental_notes_file_path (Optional)",
    # Inputs if action was 'finalize':
    "Output from step WF-RELEASE-NOTES-HYBRID-V3-98-FINALIZE: final_release_notes_path (Optional)",
    "Output from step WF-RELEASE-NOTES-HYBRID-V3-98-FINALIZE: github_release_url (Optional)",
    # Error context (always possible)
    "ErrorContext: error_message (String, Optional)",
    "ErrorContext: failing_step_id (String, Optional)"
]
outputs = [
    "workflow_result: Summary string detailing success/failure, path to local file, and GitHub push outcome."
]

# --- Housekeeping ---
last_updated = "{{DATE}}" # Placeholder
template_schema_doc = ".ruru/templates/toml-md/26_workflow_step_finish.md"
+++

# Step 99: Finish Workflow & Report

## Actions

1.  **Check for Errors:** If `error_message` or `failing_step_id` are present, prioritize reporting the failure.
2.  **Determine Action Taken:** Check the value of `determined_action`.
3.  **Generate Report based on Action:**
    *   **If `determined_action` was "add":**
        *   Check if `incremental_notes_file_path` exists.
        *   Report success: "Action 'add' completed. Incremental notes saved to: `{{incremental_notes_file_path}}`."
        *   Report failure (if file path missing and no error context): "Action 'add' failed to generate incremental notes file."
    *   **If `determined_action` was "finalize":**
        *   Check if `final_release_notes_path` exists.
        *   Base success on `final_release_notes_path` existing.
        *   Report success: "Action 'finalize' completed. Final release notes generated at: `{{final_release_notes_path}}`. {{'GitHub Release: ' + github_release_url if github_release_url else 'GitHub push skipped or failed.'}}"
        *   Report failure (if file path missing and no error context): "Action 'finalize' failed to generate final release notes file."
        *   Report partial failure (if file exists but `github_release_url` is missing when push was intended): "Action 'finalize' completed with warnings. Final notes at: `{{final_release_notes_path}}`. GitHub push failed."
    *   **If Error Context Exists:**
        *   Report failure: "Workflow failed during step `{{failing_step_id}}`. Error: {{error_message}}." Include details about partial file creation if available.
4.  **Construct `workflow_result`:** Assign the generated report string to the `workflow_result` output.

## Acceptance Criteria

*   The `workflow_result` output contains a clear summary message reflecting the outcome of the specific action ('add' or 'finalize') or any preceding errors.
*   The summary includes relevant file paths or URLs generated by the preceding steps.

## Error Handling

*   Errors in this final step should be logged, and the `workflow_result` should indicate a failure in final reporting.