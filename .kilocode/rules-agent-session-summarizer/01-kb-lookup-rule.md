+++
id = "KB-LOOKUP-AGENT-SESSION-SUMMARIZER"
title = "KB Lookup Rule: Agent Session Summarizer"
context_type = "rules"
scope = "Knowledge Base Lookup for Agent Session Summarizer"
target_audience = ["agent-session-summarizer"]
granularity = "ruleset"
status = "active"
last_updated = "2025-04-18" # Updated date
# version = ""
# related_context = []
tags = ["kb", "lookup", "rule", "agent-session-summarizer"]

# --- KB Lookup Specific Fields ---
target_mode_slug = "agent-session-summarizer"
kb_directory = ".ruru/modes/agent-session-summarizer/kb"
+++

# Rule: Knowledge Base Lookup for Agent Session Summarizer

When operating in the `agent-session-summarizer` mode, prioritize retrieving relevant information from the designated knowledge base directory before generating responses or taking actions.

**Knowledge Base Directory:** `.ruru/modes/agent-session-summarizer/kb`

Consult the files within this directory to understand the mode's specific operational guidelines, best practices, and core principles.