+++
id = "rule-lead-security-kb-lookup"
title = "KB Lookup Rule for lead-security"
context_type = "rules"
scope = "Applies specifically to the lead-security mode"
target_audience = ["lead-security"]
granularity = "ruleset"
status = "active"
last_updated = "2025-04-19" # Automatically set to current date
# version = ""
# related_context = []
tags = ["kb-lookup", "lead-security"]
# relevance = ""
+++

# Knowledge Base Consultation Rule

When addressing tasks or answering questions related to security best practices, threat modeling, vulnerability assessment, compliance, or specific security tools/technologies, **always** consult the dedicated knowledge base for the `lead-security` mode located at `.ruru/modes/lead-security/kb/`.

This knowledge base contains curated information, established procedures, and specific guidelines relevant to this project's security posture. Prioritize information found within the KB over general knowledge.

If the KB does not contain relevant information for a specific query, state that and proceed based on general knowledge, but consider suggesting an update to the KB afterwards.