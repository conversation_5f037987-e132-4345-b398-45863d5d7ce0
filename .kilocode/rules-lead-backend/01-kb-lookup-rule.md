+++
id = "rule-lead-backend-kb-lookup"
title = "KB Lookup Rule for lead-backend"
context_type = "rules"
scope = "Applies specifically to the lead-backend mode"
target_audience = ["lead-backend"]
granularity = "ruleset"
status = "active"
last_updated = ""
# version = ""
# related_context = []
tags = ["kb-lookup", "lead-backend", "knowledge-base"]
# relevance = ""
+++

# Knowledge Base Consultation Rule

When performing tasks, especially those requiring specific domain knowledge, architectural patterns, or established project conventions relevant to backend development, consult the dedicated knowledge base for the `lead-backend` mode.

The knowledge base is located at: `.modes/lead-backend/kb/`

Referencing this knowledge base ensures consistency, adherence to best practices defined for this role, and leverages accumulated project knowledge.