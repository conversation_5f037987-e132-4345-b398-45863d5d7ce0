+++
id = "util-senior-dev-kb-lookup"
title = "KB Lookup Rule for util-senior-dev"
context_type = "rules"
scope = "Mode-specific rule for knowledge base interaction"
target_audience = ["util-senior-dev"]
granularity = "ruleset"
status = "active"
last_updated = "2025-04-19" # Assuming today's date
# version = ""
# related_context = []
tags = ["kb", "knowledge-base", "lookup", "util-senior-dev"]
# relevance = ""
target_mode_slug = "util-senior-dev"
kb_directory = ".ruru/modes/util-senior-dev/kb/"
+++

# Knowledge Base Consultation Rule

Before attempting any task, **ALWAYS** consult the knowledge base (KB) files located within the `.ruru/modes/util-senior-dev/kb/` directory. These files contain specific guidelines, best practices, code snippets, and known issues relevant to your function.

Prioritize information found in the KB over general knowledge. If the KB doesn't cover the specific situation, proceed using your core expertise, but consider if new information should be added to the KB for future reference.